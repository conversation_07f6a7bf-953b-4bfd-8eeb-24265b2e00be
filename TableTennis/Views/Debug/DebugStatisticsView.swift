import SwiftUI

struct DebugStatisticsView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var selectedPlayer: Player?
    @State private var debugOutput: String = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Debug Statistics")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Use this view to debug player statistics issues")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // Player selection
                VStack(alignment: .leading, spacing: 12) {
                    Text("Select Player to Debug:")
                        .font(.headline)
                    
                    Picker("Player", selection: $selectedPlayer) {
                        Text("Select a player").tag(nil as Player?)
                        ForEach(dataManager.players, id: \.id) { player in
                            Text("\(player.name) - \(player.matchesPlayed) matches").tag(player as Player?)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                // Debug buttons
                HStack(spacing: 16) {
                    <PERSON><PERSON>("Debug Selected Player") {
                        if let player = selectedPlayer {
                            debugOutput = ""
                            dataManager.debugPlayerStatistics(for: player)
                        }
                    }
                    .disabled(selectedPlayer == nil)
                    .buttonStyle(.borderedProminent)
                    
                    But<PERSON>("Force Recalculate All") {
                        debugOutput = ""
                        dataManager.forceRecalculateAllStatistics()
                    }
                    .buttonStyle(.bordered)
                }
                
                // Current statistics overview
                if !dataManager.players.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Player Statistics:")
                            .font(.headline)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(dataManager.players, id: \.id) { player in
                                    HStack {
                                        Text(player.name)
                                            .fontWeight(.medium)
                                        Spacer()
                                        Text("Matches: \(player.matchesPlayed)/\(player.matchesWon)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        Text("Games: \(player.gamesPlayed)/\(player.gamesWon)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .padding(.horizontal)
                                    .padding(.vertical, 4)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(8)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                }
                
                Spacer()
                
                Text("Check the Xcode console for detailed debug output")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding()
        }
    }
}

#Preview {
    DebugStatisticsView()
        .environmentObject(DataManager())
}
