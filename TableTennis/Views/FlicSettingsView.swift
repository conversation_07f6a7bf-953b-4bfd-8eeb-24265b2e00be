import SwiftUI

#if !targetEnvironment(simulator)
import flic2lib
#endif

struct FlicSettingsView: View {
    @EnvironmentObject var flicManager: FlicManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            List {
                // Introductie sectie
                Section {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "dot.radiowaves.left.and.right")
                                .foregroundColor(.blue)
                                .font(.title2)
                            
                            Text("Flic Buttons")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        
                        Text("Verbind Flic buttons voor snelle scoring tijdens live wedstrijden. Elke button kan worden toegewezen aan een speler of team.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .padding(.vertical, 4)
                } header: {
                    Text("Over Flic Buttons")
                }
                
                // Button configuratie sectie
                Section {
                    FlicButtonRow(
                        type: .team1,
                        status: flicManager.team1Status,
                        buttonName: flicManager.team1ButtonName
                    ) {
                        handleButtonAction(type: .team1)
                    }
                    
                    FlicButtonRow(
                        type: .team2,
                        status: flicManager.team2Status,
                        buttonName: flicManager.team2ButtonName
                    ) {
                        handleButtonAction(type: .team2)
                    }
                } header: {
                    Text("Button Configuratie")
                } footer: {
                    Text("Tik op 'Verbinden' om een Flic button toe te wijzen. Tijdens live wedstrijden kun je de buttons gebruiken om snel punten toe te voegen.\n\nEnkelklik: Punt voor je eigen team\nDubbelklik: Punt voor de tegenstander")
                }
                
                // Status sectie
                if flicManager.bothButtonsConnected {
                    Section {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            
                            Text("Beide buttons zijn verbonden")
                                .fontWeight(.medium)
                            
                            Spacer()
                            
                            Text("Klaar voor live scoring!")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } header: {
                        Text("Status")
                    }
                }
                
                // Acties sectie
                Section {
                    Button(role: .destructive) {
                        flicManager.resetAllConnections()
                    } label: {
                        HStack {
                            Image(systemName: "trash")
                            Text("Reset Alle Verbindingen")
                        }
                    }
                    .disabled(!hasAnyConnection)

                    Button {
                        flicManager.checkConnectionStatus()
                    } label: {
                        HStack {
                            Image(systemName: "info.circle")
                            Text("Check Verbindingsstatus")
                        }
                    }

                    #if !targetEnvironment(simulator)
                    Button {
                        flicManager.stopScanning()
                    } label: {
                        HStack {
                            Image(systemName: "stop.circle")
                            Text("Stop Scannen")
                        }
                    }
                    #endif
                } header: {
                    Text("Acties")
                } footer: {
                    Text("Reset verbreekt alle verbindingen. Check status toont debug info in console.")
                }
            }
            .navigationTitle("Flic Buttons")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Klaar") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var hasAnyConnection: Bool {
        switch flicManager.team1Status {
        case .connected: return true
        default: break
        }
        
        switch flicManager.team2Status {
        case .connected: return true
        default: return false
        }
    }
    
    private func handleButtonAction(type: FlicButtonType) {
        switch type {
        case .team1:
            if case .connected = flicManager.team1Status {
                flicManager.disconnectButton(type: .team1)
            } else {
                flicManager.connectButton(type: .team1)
            }
        case .team2:
            if case .connected = flicManager.team2Status {
                flicManager.disconnectButton(type: .team2)
            } else {
                flicManager.connectButton(type: .team2)
            }
        }
    }
}

struct FlicButtonRow: View {
    let type: FlicButtonType
    let status: FlicConnectionStatus
    let buttonName: String
    let action: () -> Void
    
    var body: some View {
        HStack {
            // Button type indicator
            Image(systemName: type.icon)
                .foregroundColor(type.color)
                .font(.title2)
                .frame(width: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(type.displayName)
                    .font(.headline)
                
                HStack {
                    Circle()
                        .fill(status.color)
                        .frame(width: 8, height: 8)
                    
                    Text(status.displayText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                if !buttonName.isEmpty {
                    Text(buttonName)
                        .font(.caption2)
                        .foregroundColor(Color.secondary)
                }
            }
            
            Spacer()
            
            // Action button
            Button(action: action) {
                Text(buttonActionText)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(buttonActionColor)
                    .cornerRadius(8)
            }
            .disabled({
                if case .connecting = status {
                    return true
                }
                return false
            }())
        }
        .contentShape(Rectangle())
    }
    
    private var buttonActionText: String {
        switch status {
        case .disconnected, .failed:
            return "Verbinden"
        case .connecting:
            return "Bezig..."
        case .connected:
            return "Verbreken"
        }
    }
    
    private var buttonActionColor: Color {
        switch status {
        case .disconnected, .failed:
            return .blue
        case .connecting:
            return .orange
        case .connected:
            return .red
        }
    }
}

#Preview {
    FlicSettingsView()
        .environmentObject(FlicManager())
}
